/**
 * Azure App Service Deployment Test Suite
 * 
 * Tests the Zoho Payment integration on Azure App Service infrastructure
 */

const fetch = globalThis.fetch || require('node-fetch')

const APP_SERVICE_URL = 'https://aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net'
const OLD_STATIC_URL = 'https://yellow-sky-08e56d200.5.azurestaticapps.net'
const LOCAL_URL = 'http://localhost:3000'

class AppServiceDeploymentTest {
  constructor() {
    this.results = {
      app_service: { tests: [], passed: 0, failed: 0 },
      migration_verification: { tests: [], passed: 0, failed: 0 },
      compatibility: { tests: [], passed: 0, failed: 0 }
    }
  }

  async runTest(category, testName, testFunction) {
    console.log(`\n🧪 Testing: ${testName}`)
    
    const startTime = Date.now()
    let result = { success: false, error: null, data: null, duration: 0 }
    
    try {
      const testResult = await Promise.race([
        testFunction(),
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('Test timeout')), 30000)
        )
      ])
      
      result = { ...testResult, duration: Date.now() - startTime }
      
      if (result.success) {
        console.log(`✅ ${testName}: PASSED (${result.duration}ms)`)
        this.results[category].passed++
      } else {
        console.log(`❌ ${testName}: FAILED - ${result.error}`)
        this.results[category].failed++
      }
    } catch (error) {
      result = { success: false, error: error.message, duration: Date.now() - startTime }
      console.log(`❌ ${testName}: ERROR - ${error.message}`)
      this.results[category].failed++
    }
    
    this.results[category].tests.push({
      name: testName,
      ...result
    })
  }

  // Test App Service deployment status
  async testAppServiceDeployment() {
    try {
      const response = await fetch(`${APP_SERVICE_URL}/api/zoho/health`)
      
      if (!response.ok) {
        return { 
          success: false, 
          error: `HTTP ${response.status}: ${response.statusText}`,
          data: { status: response.status }
        }
      }

      const data = await response.json()
      
      if (data.status !== 'healthy') {
        return { 
          success: false, 
          error: 'Health check not healthy',
          data 
        }
      }

      return { success: true, data }
    } catch (error) {
      return { 
        success: false, 
        error: `Deployment not accessible: ${error.message}` 
      }
    }
  }

  // Test App Service domain configuration
  async testAppServiceDomainConfig() {
    try {
      const response = await fetch(`${APP_SERVICE_URL}/api/zoho/health`)
      const data = await response.json()
      
      const expectedDomain = APP_SERVICE_URL
      const actualDomain = data.configuration?.domain
      
      if (actualDomain !== expectedDomain) {
        return { 
          success: false, 
          error: `Domain mismatch. Expected: ${expectedDomain}, Got: ${actualDomain}`,
          data: { expected: expectedDomain, actual: actualDomain }
        }
      }

      return { success: true, data }
    } catch (error) {
      return { 
        success: false, 
        error: `Domain config test failed: ${error.message}` 
      }
    }
  }

  // Test App Service payment creation
  async testAppServicePaymentCreation() {
    const testPayload = {
      amount: 100.00,
      currency: 'INR',
      description: 'App Service Test Payment',
      invoice_number: `APP_SVC_${Date.now()}`,
      customer_id: 'app_service_test',
      customer_name: 'App Service Test Customer',
      customer_email: '<EMAIL>',
      customer_phone: '+91-9876543210'
    }

    try {
      const response = await fetch(`${APP_SERVICE_URL}/api/zoho/payments/create-session`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(testPayload)
      })

      if (!response.ok) {
        const errorText = await response.text()
        return { 
          success: false, 
          error: `HTTP ${response.status}: ${errorText.substring(0, 200)}` 
        }
      }

      const data = await response.json()
      
      if (!data.success || !data.data?.payment_session_id) {
        return { 
          success: false, 
          error: 'Invalid payment creation response',
          data 
        }
      }

      return { success: true, data }
    } catch (error) {
      return { 
        success: false, 
        error: `Payment creation failed: ${error.message}` 
      }
    }
  }

  // Test migration from Static Web Apps
  async testMigrationFromStaticWebApps() {
    try {
      // Test old URL still works
      const oldResponse = await fetch(`${OLD_STATIC_URL}/api/zoho/health`)
      const oldData = await oldResponse.json()
      
      // Test new URL (may not be deployed yet)
      let newWorking = false
      try {
        const newResponse = await fetch(`${APP_SERVICE_URL}/api/zoho/health`)
        if (newResponse.ok) {
          const newData = await newResponse.json()
          newWorking = newData.status === 'healthy'
        }
      } catch (e) {
        // New URL not deployed yet
      }

      return { 
        success: true, 
        data: { 
          old_url_working: oldData.status === 'healthy',
          new_url_working: newWorking,
          migration_status: newWorking ? 'completed' : 'pending'
        }
      }
    } catch (error) {
      return { 
        success: false, 
        error: `Migration test failed: ${error.message}` 
      }
    }
  }

  // Test environment variable compatibility
  async testEnvironmentCompatibility() {
    try {
      // Test local environment
      const localResponse = await fetch(`${LOCAL_URL}/api/zoho/health`)
      const localData = await localResponse.json()
      
      // Check if all required environment variables are present
      const envCheck = localData.checks?.environment
      if (!envCheck || envCheck.status !== 'healthy') {
        return { 
          success: false, 
          error: 'Environment variables not properly configured',
          data: envCheck 
        }
      }

      // Check domain configuration
      const expectedLocalDomain = 'https://aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net'
      const actualLocalDomain = localData.configuration?.domain
      
      if (actualLocalDomain !== expectedLocalDomain) {
        return { 
          success: false, 
          error: `Local domain config incorrect. Expected: ${expectedLocalDomain}, Got: ${actualLocalDomain}`,
          data: { expected: expectedLocalDomain, actual: actualLocalDomain }
        }
      }

      return { success: true, data: localData }
    } catch (error) {
      return { 
        success: false, 
        error: `Environment compatibility test failed: ${error.message}` 
      }
    }
  }

  // Test API compatibility between environments
  async testAPICompatibility() {
    try {
      // Test local API structure
      const localResponse = await fetch(`${LOCAL_URL}/api/zoho/health`)
      const localData = await localResponse.json()
      
      // Verify API structure is compatible with App Service
      const requiredFields = ['timestamp', 'service', 'version', 'status', 'checks', 'configuration']
      const missingFields = requiredFields.filter(field => !(field in localData))
      
      if (missingFields.length > 0) {
        return { 
          success: false, 
          error: `Missing required API fields: ${missingFields.join(', ')}`,
          data: { missing: missingFields }
        }
      }

      // Check API response format
      const checks = localData.checks || {}
      const requiredChecks = ['database', 'environment', 'zoho_auth', 'zoho_api']
      const missingChecks = requiredChecks.filter(check => !(check in checks))
      
      if (missingChecks.length > 0) {
        return { 
          success: false, 
          error: `Missing health checks: ${missingChecks.join(', ')}`,
          data: { missing: missingChecks }
        }
      }

      return { success: true, data: localData }
    } catch (error) {
      return { 
        success: false, 
        error: `API compatibility test failed: ${error.message}` 
      }
    }
  }

  async runAllTests() {
    console.log('🚀 Azure App Service Deployment Test Suite')
    console.log('==========================================')
    
    // App Service Tests
    await this.runTest('app_service', 'App Service Deployment Status', this.testAppServiceDeployment.bind(this))
    await this.runTest('app_service', 'App Service Domain Configuration', this.testAppServiceDomainConfig.bind(this))
    await this.runTest('app_service', 'App Service Payment Creation', this.testAppServicePaymentCreation.bind(this))
    
    // Migration Tests
    await this.runTest('migration_verification', 'Migration from Static Web Apps', this.testMigrationFromStaticWebApps.bind(this))
    
    // Compatibility Tests
    await this.runTest('compatibility', 'Environment Variable Compatibility', this.testEnvironmentCompatibility.bind(this))
    await this.runTest('compatibility', 'API Structure Compatibility', this.testAPICompatibility.bind(this))
    
    this.generateReport()
  }

  generateReport() {
    console.log('\n==========================================')
    console.log('📊 APP SERVICE DEPLOYMENT TEST REPORT')
    console.log('==========================================')
    
    let totalPassed = 0
    let totalFailed = 0
    let totalTests = 0
    
    Object.entries(this.results).forEach(([category, results]) => {
      const total = results.passed + results.failed
      const rate = total > 0 ? ((results.passed / total) * 100).toFixed(1) : '0.0'
      console.log(`\n${category.toUpperCase().replace('_', ' ')}: ${results.passed}/${total} (${rate}%)`)
      
      totalPassed += results.passed
      totalFailed += results.failed
      totalTests += total
    })
    
    const overallRate = totalTests > 0 ? ((totalPassed / totalTests) * 100).toFixed(1) : '0.0'
    console.log(`\n📈 OVERALL: ${totalPassed}/${totalTests} (${overallRate}%)`)
    
    // Deployment Status Assessment
    console.log(`\n🎯 DEPLOYMENT STATUS ASSESSMENT`)
    if (overallRate >= 80) {
      console.log('✅ READY FOR APP SERVICE - Migration configuration is correct')
    } else {
      console.log('⚠️  NEEDS ATTENTION - Some configuration issues detected')
    }
    
    return this.results
  }
}

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { AppServiceDeploymentTest, APP_SERVICE_URL, OLD_STATIC_URL }
}

// Run tests if this file is executed directly
if (typeof require !== 'undefined' && require.main === module) {
  const testSuite = new AppServiceDeploymentTest()
  testSuite.runAllTests().catch(console.error)
}
