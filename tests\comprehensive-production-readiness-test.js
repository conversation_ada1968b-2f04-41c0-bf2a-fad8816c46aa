/**
 * Comprehensive Production Readiness Test Suite
 *
 * Tests all aspects of the Zoho Payment Integration API for production deployment
 */

// Import fetch for Node.js environments
const fetch = globalThis.fetch || require('node-fetch')
const crypto = require('crypto')

const PRODUCTION_URL = 'https://yellow-sky-08e56d200.5.azurestaticapps.net'
const LOCAL_URL = 'http://localhost:3000'

// Test configuration
const TEST_CONFIG = {
  timeout: 30000, // 30 seconds
  retries: 3,
  concurrentRequests: 5,
}

// Test data
const TEST_PAYMENT_DATA = {
  amount: 100.0,
  currency: 'INR',
  description: 'Production Readiness Test Payment',
  invoice_number: `TEST_INV_${Date.now()}`,
  customer_id: 'test_customer_prod',
  customer_name: 'Test Customer Production',
  customer_email: '<EMAIL>',
  customer_phone: '+91-9876543210',
  redirect_url: `${PRODUCTION_URL}/payment/success`,
  reference_id: `REF_PROD_${Date.now()}`,
  meta_data: [
    { key: 'test_type', value: 'production_readiness' },
    { key: 'environment', value: 'production' },
  ],
}

class ProductionReadinessTestSuite {
  constructor() {
    this.results = {
      summary: {
        total_tests: 0,
        passed: 0,
        failed: 0,
        warnings: 0,
        start_time: new Date(),
        end_time: null,
      },
      categories: {
        core_api: { tests: [], passed: 0, failed: 0 },
        production_env: { tests: [], passed: 0, failed: 0 },
        security: { tests: [], passed: 0, failed: 0 },
        error_handling: { tests: [], passed: 0, failed: 0 },
        performance: { tests: [], passed: 0, failed: 0 },
        integration: { tests: [], passed: 0, failed: 0 },
        documentation: { tests: [], passed: 0, failed: 0 },
      },
      recommendations: [],
      critical_issues: [],
      performance_metrics: {},
    }
  }

  async runTest(category, testName, testFunction, environment = 'both') {
    const environments = environment === 'both' ? ['local', 'production'] : [environment]

    for (const env of environments) {
      const baseUrl = env === 'local' ? LOCAL_URL : PRODUCTION_URL
      const fullTestName = `${testName} (${env})`

      console.log(`\n🧪 Running: ${fullTestName}`)

      const startTime = Date.now()
      let result = { success: false, error: null, data: null, duration: 0 }

      try {
        const testResult = await Promise.race([
          testFunction(baseUrl),
          new Promise((_, reject) => setTimeout(() => reject(new Error('Test timeout')), TEST_CONFIG.timeout)),
        ])

        result = { ...testResult, duration: Date.now() - startTime }

        if (result.success) {
          console.log(`✅ ${fullTestName}: PASSED (${result.duration}ms)`)
          this.results.categories[category].passed++
          this.results.summary.passed++
        } else {
          console.log(`❌ ${fullTestName}: FAILED - ${result.error}`)
          this.results.categories[category].failed++
          this.results.summary.failed++
        }
      } catch (error) {
        result = { success: false, error: error.message, duration: Date.now() - startTime }
        console.log(`❌ ${fullTestName}: ERROR - ${error.message}`)
        this.results.categories[category].failed++
        this.results.summary.failed++
      }

      this.results.categories[category].tests.push({
        name: fullTestName,
        ...result,
      })

      this.results.summary.total_tests++
    }
  }

  // Core API Endpoint Tests
  async testHealthEndpoint(baseUrl) {
    const response = await fetch(`${baseUrl}/api/zoho/health`)
    const data = await response.json()

    if (!response.ok) {
      return { success: false, error: `HTTP ${response.status}`, data }
    }

    const isHealthy = data.status === 'healthy'
    const hasAllChecks = data.checks && Object.keys(data.checks).length >= 4
    const hasConfiguration =
      data.configuration &&
      data.configuration.account_id === 'configured' &&
      data.configuration.webhook_secret === 'configured'

    if (!isHealthy || !hasAllChecks || !hasConfiguration) {
      return { success: false, error: 'Health check failed validation', data }
    }

    return { success: true, data }
  }

  async testPaymentSessionCreation(baseUrl) {
    const response = await fetch(`${baseUrl}/api/zoho/payments/create-session`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(TEST_PAYMENT_DATA),
    })

    if (!response.ok) {
      const errorData = await response.text()
      return { success: false, error: `HTTP ${response.status}: ${errorData}` }
    }

    const data = await response.json()

    if (!data.success || !data.data?.payment_session_id) {
      return { success: false, error: 'Invalid payment session response', data }
    }

    return { success: true, data }
  }

  async testPaymentStatusRetrieval(baseUrl) {
    // First create a payment session to get a valid ID
    const createResponse = await this.testPaymentSessionCreation(baseUrl)
    if (!createResponse.success) {
      return { success: false, error: 'Could not create payment session for status test' }
    }

    const sessionId = createResponse.data.data.payment_session_id
    const response = await fetch(`${baseUrl}/api/zoho/payments/status/${sessionId}`)

    if (!response.ok) {
      const errorData = await response.text()
      return { success: false, error: `HTTP ${response.status}: ${errorData}` }
    }

    const data = await response.json()

    if (!data.success || !data.payment_session) {
      return { success: false, error: 'Invalid payment status response', data }
    }

    return { success: true, data }
  }

  async testPaymentsList(baseUrl) {
    const response = await fetch(`${baseUrl}/api/zoho/payments/list?customer_id=test_customer_prod&limit=5`)

    if (!response.ok) {
      const errorData = await response.text()
      return { success: false, error: `HTTP ${response.status}: ${errorData}` }
    }

    const data = await response.json()

    if (!data.success || !Array.isArray(data.transactions)) {
      return { success: false, error: 'Invalid payments list response', data }
    }

    return { success: true, data }
  }

  async testWebhookEndpoint(baseUrl) {
    const response = await fetch(`${baseUrl}/api/zoho/webhooks/payment`)

    if (!response.ok) {
      const errorData = await response.text()
      return { success: false, error: `HTTP ${response.status}: ${errorData}` }
    }

    const data = await response.json()

    if (!data.endpoint || !data.supported_events || !data.configuration) {
      return { success: false, error: 'Invalid webhook endpoint response', data }
    }

    return { success: true, data }
  }

  async testRefundCreationEndpoint(baseUrl) {
    const response = await fetch(`${baseUrl}/api/zoho/refunds/create`)

    if (!response.ok) {
      const errorData = await response.text()
      return { success: false, error: `HTTP ${response.status}: ${errorData}` }
    }

    const data = await response.json()

    if (!data.required_fields || !data.validation_rules) {
      return { success: false, error: 'Invalid refund creation endpoint response', data }
    }

    return { success: true, data }
  }

  // Production Environment Tests
  async testDomainConfiguration(baseUrl) {
    const response = await fetch(`${baseUrl}/api/zoho/health`)
    const data = await response.json()

    if (baseUrl.includes('localhost')) {
      // Local environment should have domain configured
      const domainConfigured = data.configuration?.domain !== 'not configured'
      return {
        success: domainConfigured,
        error: domainConfigured ? null : 'Domain not configured in local environment',
        data,
      }
    } else {
      // Production environment must have domain configured
      const domainConfigured = data.configuration?.domain === PRODUCTION_URL
      return {
        success: domainConfigured,
        error: domainConfigured ? null : 'Production domain not properly configured',
        data,
      }
    }
  }

  async testEnvironmentVariables(baseUrl) {
    const response = await fetch(`${baseUrl}/api/zoho/health`)
    const data = await response.json()

    const envCheck = data.checks?.environment
    if (!envCheck || envCheck.status !== 'healthy') {
      return { success: false, error: 'Environment variables check failed', data }
    }

    const requiredVars = envCheck.required_variables || []
    const missingVars = envCheck.missing_variables || []

    if (missingVars.length > 0) {
      return { success: false, error: `Missing variables: ${missingVars.join(', ')}`, data }
    }

    return { success: true, data }
  }

  // Security Tests
  async testInvalidPaymentData(baseUrl) {
    const invalidData = { amount: -100, currency: 'INVALID' }
    const response = await fetch(`${baseUrl}/api/zoho/payments/create-session`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(invalidData),
    })

    // Should return 400 for invalid data
    if (response.status !== 400) {
      return { success: false, error: 'Should reject invalid payment data with 400 status' }
    }

    return { success: true, data: { status: response.status } }
  }

  async testMissingRequiredFields(baseUrl) {
    const incompleteData = { amount: 100 } // Missing required fields
    const response = await fetch(`${baseUrl}/api/zoho/payments/create-session`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(incompleteData),
    })

    // Should return 400 for missing required fields
    if (response.status !== 400) {
      return { success: false, error: 'Should reject incomplete data with 400 status' }
    }

    return { success: true, data: { status: response.status } }
  }

  // Performance Tests
  async testResponseTime(baseUrl) {
    const startTime = Date.now()
    const response = await fetch(`${baseUrl}/api/zoho/health`)
    const duration = Date.now() - startTime

    if (!response.ok) {
      return { success: false, error: 'Health endpoint failed' }
    }

    // Response should be under 5 seconds
    const isAcceptable = duration < 5000
    return {
      success: isAcceptable,
      error: isAcceptable ? null : `Response time too slow: ${duration}ms`,
      data: { duration },
    }
  }

  async testConcurrentRequests(baseUrl) {
    const promises = []
    const startTime = Date.now()

    // Make 5 concurrent requests
    for (let i = 0; i < TEST_CONFIG.concurrentRequests; i++) {
      promises.push(fetch(`${baseUrl}/api/zoho/health`))
    }

    try {
      const responses = await Promise.all(promises)
      const duration = Date.now() - startTime

      const allSuccessful = responses.every((r) => r.ok)
      if (!allSuccessful) {
        return { success: false, error: 'Some concurrent requests failed' }
      }

      return {
        success: true,
        data: {
          concurrent_requests: TEST_CONFIG.concurrentRequests,
          total_duration: duration,
          avg_duration: duration / TEST_CONFIG.concurrentRequests,
        },
      }
    } catch (error) {
      return { success: false, error: `Concurrent requests failed: ${error.message}` }
    }
  }

  // Integration Tests
  async testEndToEndPaymentFlow(baseUrl) {
    try {
      // Step 1: Create payment session
      const createResult = await this.testPaymentSessionCreation(baseUrl)
      if (!createResult.success) {
        return { success: false, error: 'Failed to create payment session' }
      }

      const sessionId = createResult.data.data.payment_session_id

      // Step 2: Check payment status
      const statusResponse = await fetch(`${baseUrl}/api/zoho/payments/status/${sessionId}`)
      if (!statusResponse.ok) {
        return { success: false, error: 'Failed to retrieve payment status' }
      }

      const statusData = await statusResponse.json()
      if (!statusData.success) {
        return { success: false, error: 'Payment status check failed' }
      }

      // Step 3: Verify transaction is in database
      const listResponse = await fetch(
        `${baseUrl}/api/zoho/payments/list?customer_id=${TEST_PAYMENT_DATA.customer_id}&limit=1`
      )
      if (!listResponse.ok) {
        return { success: false, error: 'Failed to list transactions' }
      }

      const listData = await listResponse.json()
      if (!listData.success || !Array.isArray(listData.transactions)) {
        return { success: false, error: 'Transaction list check failed' }
      }

      return {
        success: true,
        data: {
          session_id: sessionId,
          status: statusData.payment_session?.status,
          transaction_count: listData.transactions.length,
        },
      }
    } catch (error) {
      return { success: false, error: `End-to-end flow failed: ${error.message}` }
    }
  }

  // Documentation Compliance Tests
  async testAPIResponseFormat(baseUrl) {
    const response = await fetch(`${baseUrl}/api/zoho/health`)
    const data = await response.json()

    // Check required fields according to documentation
    const requiredFields = ['timestamp', 'service', 'version', 'status', 'checks', 'configuration']
    const missingFields = requiredFields.filter((field) => !(field in data))

    if (missingFields.length > 0) {
      return { success: false, error: `Missing required fields: ${missingFields.join(', ')}` }
    }

    // Check configuration structure
    const config = data.configuration
    const requiredConfigFields = ['account_id', 'webhook_secret', 'domain']
    const missingConfigFields = requiredConfigFields.filter((field) => !(field in config))

    if (missingConfigFields.length > 0) {
      return { success: false, error: `Missing config fields: ${missingConfigFields.join(', ')}` }
    }

    return { success: true, data }
  }

  async testErrorResponseFormat(baseUrl) {
    // Test with invalid endpoint
    const response = await fetch(`${baseUrl}/api/zoho/payments/status/invalid_session_id`)

    if (response.ok) {
      return { success: false, error: 'Should return error for invalid session ID' }
    }

    const data = await response.json()

    // Check error response format
    if (!data.error && !data.message) {
      return { success: false, error: 'Error response missing error/message fields' }
    }

    return { success: true, data }
  }

  async runAllTests() {
    console.log('🚀 Starting Comprehensive Production Readiness Test Suite')
    console.log('=========================================================')

    // Core API Endpoint Tests
    await this.runTest('core_api', 'Health Endpoint', this.testHealthEndpoint.bind(this))
    await this.runTest('core_api', 'Payment Session Creation', this.testPaymentSessionCreation.bind(this))
    await this.runTest('core_api', 'Payment Status Retrieval', this.testPaymentStatusRetrieval.bind(this))
    await this.runTest('core_api', 'Payments List', this.testPaymentsList.bind(this))
    await this.runTest('core_api', 'Webhook Endpoint', this.testWebhookEndpoint.bind(this))
    await this.runTest('core_api', 'Refund Creation Endpoint', this.testRefundCreationEndpoint.bind(this))

    // Production Environment Tests
    await this.runTest('production_env', 'Domain Configuration', this.testDomainConfiguration.bind(this))
    await this.runTest('production_env', 'Environment Variables', this.testEnvironmentVariables.bind(this))

    // Security Tests
    await this.runTest('security', 'Invalid Payment Data Rejection', this.testInvalidPaymentData.bind(this))
    await this.runTest('security', 'Missing Required Fields Rejection', this.testMissingRequiredFields.bind(this))

    // Performance Tests
    await this.runTest('performance', 'Response Time', this.testResponseTime.bind(this))
    await this.runTest('performance', 'Concurrent Requests', this.testConcurrentRequests.bind(this))

    // Integration Tests
    await this.runTest('integration', 'End-to-End Payment Flow', this.testEndToEndPaymentFlow.bind(this))

    // Documentation Compliance Tests
    await this.runTest('documentation', 'API Response Format', this.testAPIResponseFormat.bind(this))
    await this.runTest('documentation', 'Error Response Format', this.testErrorResponseFormat.bind(this))

    this.results.summary.end_time = new Date()
    this.generateReport()
  }

  generateReport() {
    console.log('\n=========================================================')
    console.log('📊 COMPREHENSIVE PRODUCTION READINESS TEST REPORT')
    console.log('=========================================================')

    const duration = this.results.summary.end_time - this.results.summary.start_time
    const successRate = ((this.results.summary.passed / this.results.summary.total_tests) * 100).toFixed(1)

    console.log(`\n📈 SUMMARY`)
    console.log(`Total Tests: ${this.results.summary.total_tests}`)
    console.log(`Passed: ${this.results.summary.passed}`)
    console.log(`Failed: ${this.results.summary.failed}`)
    console.log(`Success Rate: ${successRate}%`)
    console.log(`Duration: ${Math.round(duration / 1000)}s`)

    console.log(`\n📋 CATEGORY BREAKDOWN`)
    Object.entries(this.results.categories).forEach(([category, results]) => {
      const total = results.passed + results.failed
      const rate = total > 0 ? ((results.passed / total) * 100).toFixed(1) : '0.0'
      console.log(`${category.toUpperCase()}: ${results.passed}/${total} (${rate}%)`)
    })

    // Production Readiness Assessment
    console.log(`\n🎯 PRODUCTION READINESS ASSESSMENT`)
    if (successRate >= 95) {
      console.log('✅ READY FOR PRODUCTION - All critical tests passing')
    } else if (successRate >= 80) {
      console.log('⚠️  NEEDS ATTENTION - Some issues need resolution before production')
    } else {
      console.log('❌ NOT READY - Critical issues must be resolved')
    }

    return this.results
  }
}

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { ProductionReadinessTestSuite, TEST_CONFIG, PRODUCTION_URL, LOCAL_URL }
}

// Run tests if this file is executed directly
if (typeof require !== 'undefined' && require.main === module) {
  const testSuite = new ProductionReadinessTestSuite()
  testSuite.runAllTests().catch(console.error)
}
