name: Azure App Service CI/CD

on:
  push:
    branches:
      - main
  pull_request:
    types: [opened, synchronize, reopened, closed]
    branches:
      - main

jobs:
  build_and_deploy:
    # Don't run on closed PRs
    if: github.event_name != 'pull_request' || github.event.action != 'closed'
    runs-on: ubuntu-latest
    name: Build and Deploy

    steps:
      # Check out the repository
      - uses: actions/checkout@v3
        with:
          submodules: true
          lfs: false

      # Set up Node.js environment
      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'

      # Install dependencies
      - name: Install dependencies
        run: npm ci

      # Build the Next.js application (without static export)
      - name: Build Next.js app
        run: npm run build
        env:
          # Domain Configuration
          NEXT_PUBLIC_DOMAIN: https://aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net
          NEXT_PUBLIC_API_DOMAIN: /api

          # Database Configuration
          MONGODB_URI: ${{ secrets.MONGODB_URI }}

          # Zoho Payment Configuration
          ZOHO_PAYMENT_SESSION_URL: ${{ secrets.ZOHO_PAYMENT_SESSION_URL }}
          ZOHO_PAY_ACCOUNT_ID: ${{ secrets.ZOHO_PAY_ACCOUNT_ID }}
          ZOHO_PAY_API_KEY: ${{ secrets.ZOHO_PAY_API_KEY }}
          ZOHO_OAUTH_CLIENT_ID: ${{ secrets.ZOHO_OAUTH_CLIENT_ID }}
          ZOHO_OAUTH_CLIENT_SECRET: ${{ secrets.ZOHO_OAUTH_CLIENT_SECRET }}
          ZOHO_OAUTH_REFRESH_TOKEN: ${{ secrets.ZOHO_OAUTH_REFRESH_TOKEN }}

          # Webhook Configuration
          ZOHO_WEBHOOK_SECRET: ${{ secrets.ZOHO_WEBHOOK_SECRET }}

          # Environment Configuration
          NODE_ENV: production

      # Create web.config for Azure App Service
      - name: Create web.config
        run: |
          cat > ./web.config << 'EOL'
          <?xml version="1.0" encoding="utf-8"?>
          <configuration>
            <system.webServer>
              <webSocket enabled="false" />
              <handlers>
                <add name="iisnode" path="server.js" verb="*" modules="iisnode" />
              </handlers>
              <rewrite>
                <rules>
                  <rule name="NextJS" patternSyntax="ECMAScript" stopProcessing="true">
                    <match url="(.*)" />
                    <conditions logicalGrouping="MatchAll">
                      <add input="{REQUEST_FILENAME}" matchType="IsFile" negate="true" />
                    </conditions>
                    <action type="Rewrite" url="server.js" />
                  </rule>
                </rules>
              </rewrite>
              <iisnode watchedFiles="web.config;*.js" />
            </system.webServer>
          </configuration>
          EOL

      # Create server.js for Azure App Service
      - name: Create server.js
        run: |
          cat > ./server.js << 'EOL'
          const { createServer } = require('http');
          const { parse } = require('url');
          const next = require('next');

          const dev = process.env.NODE_ENV !== 'production';
          const app = next({ dev });
          const handle = app.getRequestHandler();
          const port = process.env.PORT || 8080;

          app.prepare().then(() => {
            createServer((req, res) => {
              const parsedUrl = parse(req.url, true);
              handle(req, res, parsedUrl);
            }).listen(port, (err) => {
              if (err) throw err;
              console.log(`> Ready on http://localhost:${port}`);
            });
          });
          EOL

      # Zip the application for deployment
      - name: Zip artifact for deployment
        run: zip -r release.zip . -x "node_modules/*" ".git/*" ".github/*"

      # Deploy to Azure App Service using publish profile
      - name: Deploy to Azure App Service
        uses: azure/webapps-deploy@v2
        with:
          app-name: 'aquapartner'
          publish-profile: ${{ secrets.AZURE_PUBLISH_PROFILE }}
          package: release.zip

      # Configure App Settings (Environment Variables) in Azure App Service
      - name: Configure App Settings
        uses: azure/appservice-settings@v1
        with:
          app-name: 'aquapartner'
          app-settings-json: |
            [
              {
                "name": "NEXT_PUBLIC_DOMAIN",
                "value": "https://aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net",
                "slotSetting": false
              },
              {
                "name": "NEXT_PUBLIC_API_DOMAIN",
                "value": "/api",
                "slotSetting": false
              },
              {
                "name": "MONGODB_URI",
                "value": "${{ secrets.MONGODB_URI }}",
                "slotSetting": false
              },
              {
                "name": "ZOHO_PAYMENT_SESSION_URL",
                "value": "${{ secrets.ZOHO_PAYMENT_SESSION_URL }}",
                "slotSetting": false
              },
              {
                "name": "ZOHO_PAY_ACCOUNT_ID",
                "value": "${{ secrets.ZOHO_PAY_ACCOUNT_ID }}",
                "slotSetting": false
              },
              {
                "name": "ZOHO_PAY_API_KEY",
                "value": "${{ secrets.ZOHO_PAY_API_KEY }}",
                "slotSetting": false
              },
              {
                "name": "ZOHO_OAUTH_CLIENT_ID",
                "value": "${{ secrets.ZOHO_OAUTH_CLIENT_ID }}",
                "slotSetting": false
              },
              {
                "name": "ZOHO_OAUTH_CLIENT_SECRET",
                "value": "${{ secrets.ZOHO_OAUTH_CLIENT_SECRET }}",
                "slotSetting": false
              },
              {
                "name": "ZOHO_OAUTH_REFRESH_TOKEN",
                "value": "${{ secrets.ZOHO_OAUTH_REFRESH_TOKEN }}",
                "slotSetting": false
              },
              {
                "name": "ZOHO_WEBHOOK_SECRET",
                "value": "${{ secrets.ZOHO_WEBHOOK_SECRET }}",
                "slotSetting": false
              },
              {
                "name": "NODE_ENV",
                "value": "production",
                "slotSetting": false
              },
              {
                "name": "WEBSITE_NODE_DEFAULT_VERSION",
                "value": "~18",
                "slotSetting": false
              }
            ]
          azure-credentials: |
            {
              "clientId": "${{ secrets.AZURE_CLIENT_ID }}",
              "clientSecret": "${{ secrets.AZURE_CLIENT_SECRET }}",
              "subscriptionId": "${{ secrets.AZURE_SUBSCRIPTION_ID }}",
              "tenantId": "${{ secrets.AZURE_TENANT_ID }}"
            }

      # Post-deployment health check
      - name: Health check
        run: |
          echo "Waiting for deployment to complete..."
          sleep 60
          HEALTH_URL="https://aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net/api/zoho/health"
          echo "Checking health at $HEALTH_URL"
          curl -s $HEALTH_URL || echo "Health check endpoint not available yet. This is expected for first deployment."
