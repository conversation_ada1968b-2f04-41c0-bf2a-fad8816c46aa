/**
 * Production Payment Service Test Suite
 *
 * Tests the Zoho Payment integration against production environment
 */

// Import fetch for Node.js environments
const fetch = globalThis.fetch || require('node-fetch')

const PRODUCTION_URL = 'https://yellow-sky-08e56d200.5.azurestaticapps.net'
const LOCAL_URL = 'http://localhost:3000'

/**
 * Test health check endpoint
 */
async function testHealthCheck(baseUrl = LOCAL_URL) {
  console.log(`\n=== Testing Health Check (${baseUrl}) ===`)

  try {
    const response = await fetch(`${baseUrl}/api/zoho/health`)
    const data = await response.json()

    console.log('Health check response:', JSON.stringify(data, null, 2))

    const isHealthy = data.status === 'healthy'
    const domainConfigured = data.configuration?.domain !== 'not configured'

    if (isHealthy && domainConfigured) {
      console.log('✅ Health check passed - Service is healthy and domain is configured')
      return { success: true, data }
    } else {
      console.log('❌ Health check failed')
      return { success: false, error: data }
    }
  } catch (error) {
    console.log('❌ Health check request failed:', error.message)
    return { success: false, error: error.message }
  }
}

/**
 * Test payment session creation
 */
async function testPaymentSessionCreation(baseUrl = LOCAL_URL) {
  console.log(`\n=== Testing Payment Session Creation (${baseUrl}) ===`)

  const testPaymentData = {
    amount: 100.0,
    currency: 'INR',
    description: 'Test Payment for Production Integration',
    invoice_number: `TEST_INV_${Date.now()}`,
    customer_id: 'test_customer_123',
    customer_name: 'Test Customer',
    customer_email: '<EMAIL>',
    customer_phone: '+91-**********',
    redirect_url: `${baseUrl}/payment/success`,
    reference_id: `REF_${Date.now()}`,
    meta_data: [
      { key: 'test_mode', value: 'production_test' },
      { key: 'environment', value: baseUrl.includes('localhost') ? 'local' : 'production' },
    ],
  }

  try {
    const response = await fetch(`${baseUrl}/api/zoho/payment/create`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testPaymentData),
    })

    const data = await response.json()
    console.log('Payment session creation response:', JSON.stringify(data, null, 2))

    if (response.ok && data.success && data.payment_session_id) {
      console.log('✅ Payment session created successfully')
      console.log(`Payment Session ID: ${data.payment_session_id}`)
      console.log(`Payment URL: ${data.payment_url}`)
      return { success: true, data }
    } else {
      console.log('❌ Payment session creation failed')
      return { success: false, error: data }
    }
  } catch (error) {
    console.log('❌ Payment session creation request failed:', error.message)
    return { success: false, error: error.message }
  }
}

/**
 * Test payment status retrieval
 */
async function testPaymentStatusRetrieval(baseUrl = LOCAL_URL, paymentSessionId = null) {
  console.log(`\n=== Testing Payment Status Retrieval (${baseUrl}) ===`)

  // Use a test payment session ID if none provided
  const testSessionId = paymentSessionId || 'test_session_id_123'

  try {
    const response = await fetch(`${baseUrl}/api/zoho/payment/status/${testSessionId}`)
    const data = await response.json()

    console.log('Payment status response:', JSON.stringify(data, null, 2))

    if (response.ok) {
      console.log('✅ Payment status endpoint is accessible')
      return { success: true, data }
    } else {
      console.log('❌ Payment status retrieval failed')
      return { success: false, error: data }
    }
  } catch (error) {
    console.log('❌ Payment status request failed:', error.message)
    return { success: false, error: error.message }
  }
}

/**
 * Test webhook endpoint accessibility
 */
async function testWebhookEndpoint(baseUrl = LOCAL_URL) {
  console.log(`\n=== Testing Webhook Endpoint (${baseUrl}) ===`)

  try {
    const response = await fetch(`${baseUrl}/api/zoho/webhooks/payment`)
    const data = await response.json()

    console.log('Webhook endpoint response:', JSON.stringify(data, null, 2))

    if (response.ok && data.endpoint) {
      console.log('✅ Webhook endpoint is accessible')
      return { success: true, data }
    } else {
      console.log('❌ Webhook endpoint failed')
      return { success: false, error: data }
    }
  } catch (error) {
    console.log('❌ Webhook endpoint request failed:', error.message)
    return { success: false, error: error.message }
  }
}

/**
 * Test environment configuration
 */
async function testEnvironmentConfiguration(baseUrl = LOCAL_URL) {
  console.log(`\n=== Testing Environment Configuration (${baseUrl}) ===`)

  try {
    const response = await fetch(`${baseUrl}/api/zoho/health`)
    const data = await response.json()

    const config = data.configuration || {}
    const checks = data.checks || {}

    console.log('Configuration Status:')
    console.log(`- Account ID: ${config.account_id}`)
    console.log(`- Webhook Secret: ${config.webhook_secret}`)
    console.log(`- Domain: ${config.domain}`)

    console.log('\nEnvironment Checks:')
    console.log(`- Database: ${checks.database?.status}`)
    console.log(`- Environment Variables: ${checks.environment?.status}`)
    console.log(`- Zoho Auth: ${checks.zoho_auth?.status}`)
    console.log(`- Zoho API: ${checks.zoho_api?.status}`)

    const allConfigured =
      config.account_id === 'configured' && config.webhook_secret === 'configured' && config.domain !== 'not configured'

    const allHealthy = Object.values(checks).every((check) => check.status === 'healthy')

    if (allConfigured && allHealthy) {
      console.log('✅ Environment configuration is complete and healthy')
      return { success: true, data }
    } else {
      console.log('❌ Environment configuration issues detected')
      return { success: false, error: data }
    }
  } catch (error) {
    console.log('❌ Environment configuration check failed:', error.message)
    return { success: false, error: error.message }
  }
}

/**
 * Run comprehensive production tests
 */
async function runProductionTests() {
  console.log('🚀 Starting Production Payment Service Tests')
  console.log('===========================================')

  const tests = [
    { name: 'Local Health Check', fn: () => testHealthCheck(LOCAL_URL) },
    { name: 'Local Environment Config', fn: () => testEnvironmentConfiguration(LOCAL_URL) },
    { name: 'Local Payment Session', fn: () => testPaymentSessionCreation(LOCAL_URL) },
    { name: 'Local Payment Status', fn: () => testPaymentStatusRetrieval(LOCAL_URL) },
    { name: 'Local Webhook Endpoint', fn: () => testWebhookEndpoint(LOCAL_URL) },
    { name: 'Production Health Check', fn: () => testHealthCheck(PRODUCTION_URL) },
    { name: 'Production Environment Config', fn: () => testEnvironmentConfiguration(PRODUCTION_URL) },
    { name: 'Production Payment Session', fn: () => testPaymentSessionCreation(PRODUCTION_URL) },
    { name: 'Production Payment Status', fn: () => testPaymentStatusRetrieval(PRODUCTION_URL) },
    { name: 'Production Webhook Endpoint', fn: () => testWebhookEndpoint(PRODUCTION_URL) },
  ]

  let passed = 0
  let failed = 0
  const results = {}

  for (const test of tests) {
    try {
      const result = await test.fn()
      results[test.name] = result

      if (result.success) {
        passed++
      } else {
        failed++
      }
    } catch (error) {
      console.log(`❌ ${test.name} threw an error:`, error.message)
      results[test.name] = { success: false, error: error.message }
      failed++
    }

    // Small delay between tests
    await new Promise((resolve) => setTimeout(resolve, 1000))
  }

  console.log('\n===========================================')
  console.log('🚀 PRODUCTION TEST SUMMARY')
  console.log('===========================================')
  console.log(`Total Tests: ${tests.length}`)
  console.log(`Passed: ${passed}`)
  console.log(`Failed: ${failed}`)
  console.log(`Success Rate: ${((passed / tests.length) * 100).toFixed(1)}%`)

  if (passed === tests.length) {
    console.log('\n🎉 All production tests passed!')
    console.log('✨ Payment service is ready for production use.')
  } else {
    console.log('\n⚠️  Some tests failed. Check the output above for details.')
  }

  return { passed, failed, total: tests.length, results }
}

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    runProductionTests,
    testHealthCheck,
    testPaymentSessionCreation,
    testPaymentStatusRetrieval,
    testWebhookEndpoint,
    testEnvironmentConfiguration,
    PRODUCTION_URL,
    LOCAL_URL,
  }
}

// Run tests if this file is executed directly
if (typeof require !== 'undefined' && require.main === module) {
  runProductionTests().catch(console.error)
}
