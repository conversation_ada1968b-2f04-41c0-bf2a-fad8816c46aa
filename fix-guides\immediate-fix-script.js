/**
 * Immediate Fix Implementation Script
 * 
 * This script applies the critical fixes needed for production readiness
 */

const fs = require('fs')
const path = require('path')

class ProductionFixImplementation {
  constructor() {
    this.fixes = []
    this.backupDir = 'backup-before-fixes'
  }

  log(message) {
    console.log(`[FIX] ${message}`)
    this.fixes.push({ message, timestamp: new Date() })
  }

  // Create backup of current files
  createBackup() {
    this.log('Creating backup of current files...')
    
    if (!fs.existsSync(this.backupDir)) {
      fs.mkdirSync(this.backupDir, { recursive: true })
    }

    // Backup critical files
    const filesToBackup = [
      'src/app/api/zoho/payments/create-session/route.js',
      'src/app/api/zoho/payments/status/[sessionId]/route.js',
      'src/app/lib/zohoPaymentService.js'
    ]

    filesToBackup.forEach(file => {
      if (fs.existsSync(file)) {
        const backupPath = path.join(this.backupDir, file)
        const backupDir = path.dirname(backupPath)
        
        if (!fs.existsSync(backupDir)) {
          fs.mkdirSync(backupDir, { recursive: true })
        }
        
        fs.copyFileSync(file, backupPath)
        this.log(`Backed up: ${file}`)
      }
    })
  }

  // Fix 1: Add missing getPaymentSession method if needed
  fixZohoPaymentService() {
    this.log('Checking ZohoPaymentService methods...')
    
    const servicePath = 'src/app/lib/zohoPaymentService.js'
    const content = fs.readFileSync(servicePath, 'utf8')
    
    // Check if getPaymentSession method exists and works correctly
    if (!content.includes('async getPaymentSession')) {
      this.log('Adding missing getPaymentSession method...')
      
      const methodToAdd = `
  /**
   * Get payment session details from Zoho
   */
  async getPaymentSession(sessionId) {
    try {
      const accessToken = await this.getValidAccessToken()
      
      const response = await fetch(
        \`\${this.baseURL}/paymentsessions/\${sessionId}?account_id=\${this.accountId}\`,
        {
          method: 'GET',
          headers: {
            'Authorization': \`Zoho-oauthtoken \${accessToken}\`,
            'Content-Type': 'application/json'
          }
        }
      )

      if (!response.ok) {
        if (response.status === 404) {
          throw new Error(\`Payment session not found: \${sessionId}\`)
        }
        throw new Error(\`Zoho API error: \${response.status} \${response.statusText}\`)
      }

      const data = await response.json()
      
      if (data.code !== 0) {
        throw new Error(\`Zoho API Error: \${data.message}\`)
      }

      return data.payments_session
    } catch (error) {
      console.error('Error retrieving payment session:', error)
      throw error
    }
  }
`
      
      // Add method before the last closing brace
      const updatedContent = content.replace(
        /(\n}\s*module\.exports)/,
        methodToAdd + '$1'
      )
      
      fs.writeFileSync(servicePath, updatedContent)
      this.log('Added getPaymentSession method')
    }
  }

  // Fix 2: Enhance status endpoint error handling
  fixStatusEndpoint() {
    this.log('Fixing payment status endpoint...')
    
    const statusPath = 'src/app/api/zoho/payments/status/[sessionId]/route.js'
    
    if (!fs.existsSync(statusPath)) {
      this.log('Status endpoint file not found, creating...')
      
      const statusDir = path.dirname(statusPath)
      if (!fs.existsSync(statusDir)) {
        fs.mkdirSync(statusDir, { recursive: true })
      }
    }

    const enhancedStatusEndpoint = `import zohoPaymentService from '@/app/lib/zohoPaymentService'

/**
 * GET /api/zoho/payments/status/[sessionId]
 * Get payment session status and details
 */
export async function GET(request, { params }) {
  try {
    const { sessionId } = params
    
    // Validate sessionId parameter
    if (!sessionId) {
      return new Response(JSON.stringify({
        error: 'Missing session ID',
        message: 'Payment session ID is required'
      }), { 
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      })
    }

    // Validate sessionId format
    if (typeof sessionId !== 'string' || sessionId.length < 5) {
      return new Response(JSON.stringify({
        error: 'Invalid session ID',
        message: 'Payment session ID format is invalid'
      }), { 
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      })
    }

    console.log(\`Fetching payment status for session: \${sessionId}\`)

    // Get payment session from Zoho with error handling
    let paymentSession
    try {
      paymentSession = await zohoPaymentService.getPaymentSession(sessionId)
    } catch (zohoError) {
      console.error('Zoho API error:', zohoError)
      
      if (zohoError.message.includes('not found') || zohoError.message.includes('404')) {
        return new Response(JSON.stringify({
          error: 'Payment session not found',
          message: \`No payment session found with ID: \${sessionId}\`
        }), { 
          status: 404,
          headers: { 'Content-Type': 'application/json' }
        })
      }
      
      throw zohoError
    }

    // Get local transaction record with error handling
    let transaction
    try {
      transaction = await zohoPaymentService.getTransaction(sessionId)
    } catch (dbError) {
      console.error('Database error:', dbError)
      transaction = null // Continue without local transaction
    }

    // Update local transaction if status changed
    if (transaction && paymentSession.status !== transaction.status) {
      try {
        const statusData = { status: paymentSession.status }
        
        if (paymentSession.payments && paymentSession.payments.length > 0) {
          const latestPayment = paymentSession.payments[paymentSession.payments.length - 1]
          statusData.payment_id = latestPayment.payment_id
          statusData.payment_method = latestPayment.payment_method
        }

        await zohoPaymentService.updateTransactionStatus(sessionId, statusData)
      } catch (updateError) {
        console.error('Failed to update transaction status:', updateError)
        // Continue without updating local status
      }
    }

    const response = {
      success: true,
      message: 'Payment status retrieved successfully',
      payment_session: paymentSession,
      data: {
        payment_session_id: paymentSession.payments_session_id,
        status: paymentSession.status,
        amount: paymentSession.amount,
        currency: paymentSession.currency,
        description: paymentSession.description,
        invoice_number: paymentSession.invoice_number,
        created_time: paymentSession.created_time,
        payments: paymentSession.payments || [],
        meta_data: paymentSession.meta_data || []
      }
    }

    // Add transaction details if available
    if (transaction) {
      response.data.transaction = {
        id: transaction._id,
        customer_id: transaction.customer_id,
        customer_name: transaction.customer_name,
        reference_id: transaction.reference_id,
        created_at: transaction.createdAt,
        updated_at: transaction.updatedAt
      }
    }

    return new Response(JSON.stringify(response), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    })

  } catch (error) {
    console.error('Payment status retrieval error:', error)
    
    return new Response(JSON.stringify({
      error: 'Internal server error',
      message: 'Failed to retrieve payment status',
      details: process.env.NODE_ENV === 'development' ? error.message : 'Please try again later'
    }), { 
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    })
  }
}

/**
 * PUT /api/zoho/payments/status/[sessionId]
 * Manually update payment status
 */
export async function PUT(request, { params }) {
  try {
    const { sessionId } = params
    const body = await request.json()

    if (!sessionId) {
      return new Response(JSON.stringify({
        error: 'Missing session ID',
        message: 'Payment session ID is required'
      }), { status: 400 })
    }

    const { status, payment_id, payment_method, error_code, error_message } = body

    if (!status) {
      return new Response(JSON.stringify({
        error: 'Missing status',
        message: 'Payment status is required'
      }), { status: 400 })
    }

    const validStatuses = ['created', 'pending', 'succeeded', 'failed', 'cancelled', 'expired']
    if (!validStatuses.includes(status)) {
      return new Response(JSON.stringify({
        error: 'Invalid status',
        message: \`Status must be one of: \${validStatuses.join(', ')}\`
      }), { status: 400 })
    }

    const statusData = { status, payment_id, payment_method, error_code, error_message }
    const transaction = await zohoPaymentService.updateTransactionStatus(sessionId, statusData)

    return new Response(JSON.stringify({
      success: true,
      message: 'Payment status updated successfully',
      data: {
        transaction_id: transaction._id,
        payment_session_id: transaction.payments_session_id,
        status: transaction.status,
        updated_at: transaction.updatedAt
      }
    }), { status: 200 })

  } catch (error) {
    console.error('Error updating payment status:', error)

    if (error.message.includes('Transaction not found')) {
      return new Response(JSON.stringify({
        error: 'Transaction not found',
        message: 'No transaction found with the provided session ID'
      }), { status: 404 })
    }

    return new Response(JSON.stringify({
      error: 'Status update failed',
      message: error.message
    }), { status: 500 })
  }
}
`

    fs.writeFileSync(statusPath, enhancedStatusEndpoint)
    this.log('Enhanced status endpoint with better error handling')
  }

  // Fix 3: Create Azure Static Web Apps configuration
  createAzureConfig() {
    this.log('Creating Azure Static Web Apps configuration...')
    
    const configPath = 'staticwebapp.config.json'
    const config = {
      routes: [
        {
          route: "/api/*",
          allowedRoles: ["anonymous"]
        }
      ],
      navigationFallback: {
        rewrite: "/index.html",
        exclude: ["/api/*"]
      }
    }

    fs.writeFileSync(configPath, JSON.stringify(config, null, 2))
    this.log('Created staticwebapp.config.json')
  }

  // Run all fixes
  async runAllFixes() {
    console.log('🔧 Starting Production Fix Implementation')
    console.log('========================================')

    try {
      this.createBackup()
      this.fixZohoPaymentService()
      this.fixStatusEndpoint()
      this.createAzureConfig()

      console.log('\n✅ All fixes applied successfully!')
      console.log('\nNext steps:')
      console.log('1. Test locally: npm run build && npm start')
      console.log('2. Run verification: node tests/quick-fix-verification.js')
      console.log('3. Deploy: git add . && git commit -m "Apply production fixes" && git push')
      
      return true
    } catch (error) {
      console.error('❌ Fix implementation failed:', error)
      console.log('Backup files are available in:', this.backupDir)
      return false
    }
  }
}

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { ProductionFixImplementation }
}

// Run fixes if this file is executed directly
if (typeof require !== 'undefined' && require.main === module) {
  const fixer = new ProductionFixImplementation()
  fixer.runAllFixes().catch(console.error)
}`
