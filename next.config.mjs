/** @type {import('next').NextConfig} */
const nextConfig = {
  // Explicitly set output for App Service
  output: 'standalone',
  reactStrictMode: false,
  productionBrowserSourceMaps: false,
  webpack: (config, { dev }) => {
    if (dev) {
      config.devtool = false
    }
    return config
  },
  experimental: {
    missingSuspenseWithCSRBailout: false,
  },
  // Add trailing slash configuration for better compatibility with App Service
  trailingSlash: true,
}

export default nextConfig
