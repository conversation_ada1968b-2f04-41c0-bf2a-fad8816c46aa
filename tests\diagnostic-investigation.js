/**
 * Diagnostic Investigation Suite
 * 
 * Deep dive into specific issues found in production readiness testing
 */

const fetch = globalThis.fetch || require('node-fetch')

const PRODUCTION_URL = 'https://yellow-sky-08e56d200.5.azurestaticapps.net'
const LOCAL_URL = 'http://localhost:3000'

class DiagnosticInvestigation {
  constructor() {
    this.findings = []
  }

  log(message, data = null) {
    console.log(message)
    if (data) {
      console.log(JSON.stringify(data, null, 2))
    }
    this.findings.push({ message, data, timestamp: new Date() })
  }

  async investigatePaymentSessionCreation() {
    console.log('\n🔍 INVESTIGATING: Payment Session Creation Issues')
    console.log('================================================')

    const testPayload = {
      amount: 100.00,
      currency: 'INR',
      description: 'Diagnostic Test Payment',
      invoice_number: `DIAG_${Date.now()}`,
      customer_id: 'diag_customer',
      customer_name: 'Diagnostic Customer',
      customer_email: '<EMAIL>',
      customer_phone: '+91-**********',
      redirect_url: `${PRODUCTION_URL}/payment/success`,
      reference_id: `DIAG_REF_${Date.now()}`
    }

    // Test both environments
    for (const [env, baseUrl] of [['Local', LOCAL_URL], ['Production', PRODUCTION_URL]]) {
      this.log(`\n--- Testing ${env} Environment ---`)
      
      try {
        // Test endpoint accessibility
        const healthResponse = await fetch(`${baseUrl}/api/zoho/health`)
        this.log(`Health check status: ${healthResponse.status}`)
        
        // Test payment session creation
        const response = await fetch(`${baseUrl}/api/zoho/payments/create-session`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(testPayload)
        })
        
        this.log(`Payment session response status: ${response.status}`)
        this.log(`Response headers:`, Object.fromEntries(response.headers.entries()))
        
        const responseText = await response.text()
        this.log(`Raw response:`, responseText.substring(0, 500))
        
        try {
          const responseData = JSON.parse(responseText)
          this.log(`Parsed response:`, responseData)
        } catch (parseError) {
          this.log(`JSON parse error: ${parseError.message}`)
          this.log(`Response appears to be HTML or non-JSON`)
        }
        
      } catch (error) {
        this.log(`Network error: ${error.message}`)
      }
    }
  }

  async investigateAPIEndpoints() {
    console.log('\n🔍 INVESTIGATING: API Endpoint Accessibility')
    console.log('============================================')

    const endpoints = [
      '/api/zoho/health',
      '/api/zoho/payments/create-session',
      '/api/zoho/payments/list',
      '/api/zoho/payments/status/test',
      '/api/zoho/webhooks/payment',
      '/api/zoho/refunds/create'
    ]

    for (const [env, baseUrl] of [['Local', LOCAL_URL], ['Production', PRODUCTION_URL]]) {
      this.log(`\n--- ${env} Environment Endpoints ---`)
      
      for (const endpoint of endpoints) {
        try {
          const response = await fetch(`${baseUrl}${endpoint}`)
          this.log(`${endpoint}: ${response.status} ${response.statusText}`)
          
          if (response.status === 404) {
            this.log(`❌ Endpoint not found: ${endpoint}`)
          } else if (response.status >= 500) {
            this.log(`❌ Server error on: ${endpoint}`)
          }
        } catch (error) {
          this.log(`❌ Network error on ${endpoint}: ${error.message}`)
        }
      }
    }
  }

  async investigateValidationIssues() {
    console.log('\n🔍 INVESTIGATING: Input Validation Issues')
    console.log('========================================')

    const invalidPayloads = [
      {
        name: 'Negative Amount',
        payload: { amount: -100, currency: 'INR' }
      },
      {
        name: 'Invalid Currency',
        payload: { amount: 100, currency: 'INVALID' }
      },
      {
        name: 'Missing Required Fields',
        payload: { amount: 100 }
      },
      {
        name: 'Invalid Data Types',
        payload: { amount: 'not_a_number', currency: 123 }
      }
    ]

    for (const [env, baseUrl] of [['Local', LOCAL_URL], ['Production', PRODUCTION_URL]]) {
      this.log(`\n--- ${env} Environment Validation ---`)
      
      for (const test of invalidPayloads) {
        try {
          const response = await fetch(`${baseUrl}/api/zoho/payments/create-session`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(test.payload)
          })
          
          this.log(`${test.name}: ${response.status}`)
          
          if (response.status !== 400) {
            this.log(`❌ Expected 400, got ${response.status} for ${test.name}`)
            const responseText = await response.text()
            this.log(`Response: ${responseText.substring(0, 200)}`)
          } else {
            this.log(`✅ Correctly rejected ${test.name}`)
          }
        } catch (error) {
          this.log(`❌ Error testing ${test.name}: ${error.message}`)
        }
      }
    }
  }

  async investigateEnvironmentDifferences() {
    console.log('\n🔍 INVESTIGATING: Environment Configuration Differences')
    console.log('=====================================================')

    try {
      // Get health data from both environments
      const localHealth = await fetch(`${LOCAL_URL}/api/zoho/health`).then(r => r.json())
      const prodHealth = await fetch(`${PRODUCTION_URL}/api/zoho/health`).then(r => r.json())

      this.log('\n--- Configuration Comparison ---')
      this.log('Local Configuration:', localHealth.configuration)
      this.log('Production Configuration:', prodHealth.configuration)

      this.log('\n--- Environment Variables Comparison ---')
      this.log('Local Environment Check:', localHealth.checks?.environment)
      this.log('Production Environment Check:', prodHealth.checks?.environment)

      // Check for differences
      const configDiffs = []
      const localConfig = localHealth.configuration || {}
      const prodConfig = prodHealth.configuration || {}

      for (const key of Object.keys(localConfig)) {
        if (localConfig[key] !== prodConfig[key]) {
          configDiffs.push({
            key,
            local: localConfig[key],
            production: prodConfig[key]
          })
        }
      }

      if (configDiffs.length > 0) {
        this.log('\n❌ Configuration Differences Found:', configDiffs)
      } else {
        this.log('\n✅ No configuration differences detected')
      }

    } catch (error) {
      this.log(`Error comparing environments: ${error.message}`)
    }
  }

  async investigateNetworkAndRouting() {
    console.log('\n🔍 INVESTIGATING: Network and Routing Issues')
    console.log('===========================================')

    // Test different HTTP methods on payment endpoint
    const methods = ['GET', 'POST', 'PUT', 'DELETE']
    const endpoint = '/api/zoho/payments/create-session'

    for (const [env, baseUrl] of [['Local', LOCAL_URL], ['Production', PRODUCTION_URL]]) {
      this.log(`\n--- ${env} HTTP Methods Test ---`)
      
      for (const method of methods) {
        try {
          const response = await fetch(`${baseUrl}${endpoint}`, {
            method,
            headers: { 'Content-Type': 'application/json' },
            body: method === 'POST' ? JSON.stringify({ test: true }) : undefined
          })
          
          this.log(`${method} ${endpoint}: ${response.status}`)
          
          if (method === 'POST' && response.status === 404) {
            this.log(`❌ POST endpoint not found - routing issue`)
          }
        } catch (error) {
          this.log(`❌ ${method} error: ${error.message}`)
        }
      }
    }
  }

  async runFullDiagnostic() {
    console.log('🔬 Starting Comprehensive Diagnostic Investigation')
    console.log('==================================================')

    await this.investigatePaymentSessionCreation()
    await this.investigateAPIEndpoints()
    await this.investigateValidationIssues()
    await this.investigateEnvironmentDifferences()
    await this.investigateNetworkAndRouting()

    console.log('\n📋 DIAGNOSTIC SUMMARY')
    console.log('====================')
    console.log(`Total findings: ${this.findings.length}`)
    
    // Categorize findings
    const errors = this.findings.filter(f => f.message.includes('❌'))
    const successes = this.findings.filter(f => f.message.includes('✅'))
    
    console.log(`Errors found: ${errors.length}`)
    console.log(`Successful checks: ${successes.length}`)
    
    if (errors.length > 0) {
      console.log('\n🚨 Key Issues Identified:')
      errors.forEach((error, index) => {
        console.log(`${index + 1}. ${error.message}`)
      })
    }

    return this.findings
  }
}

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { DiagnosticInvestigation }
}

// Run diagnostic if this file is executed directly
if (typeof require !== 'undefined' && require.main === module) {
  const diagnostic = new DiagnosticInvestigation()
  diagnostic.runFullDiagnostic().catch(console.error)
}
